# Platform-Aware Provider and Model Management

## Overview

The system now supports multiple platforms (Requesty and OpenRouter) with the same providers and models existing independently on each platform. This document explains the enhanced logic to handle platform-aware uniqueness.

## Key Changes

### 1. Database Schema
- Added `platform` field to `Provider` model
- Platform values: `"requesty"` or `"openrouter"`
- Uniqueness is now based on `(provider_name, platform)` combination

### 2. Enhanced Import Logic

#### Provider Uniqueness
```python
# OLD: Only checked provider name
existing_provider = session.query(Provider).filter_by(provider=provider_name).first()

# NEW: Checks provider name AND platform
existing_provider = session.query(Provider).filter(
    Provider.provider == provider_name,
    Provider.platform == platform
).first()
```

#### Model Uniqueness
```python
# OLD: Only checked within same provider
existing_model = session.query(Model).filter_by(
    provider_id=provider.id,
    model=model_name
).first()

# NEW: Still checks within same provider, but provider is now platform-specific
# This automatically ensures models are unique per (provider, platform, model_name)
```

### 3. Cross-Platform Awareness

The import script now:
- ✅ Detects when a provider exists on other platforms
- ✅ Creates separate provider entries for each platform
- ✅ Shows informative messages about cross-platform duplicates
- ✅ Maintains separate model collections per platform

## Example Scenarios

### Scenario 1: OpenAI on Both Platforms

**Before Import:**
```
providers:
  - id: 1, provider: "openai", platform: "requesty"
models:
  - provider_id: 1, model: "gpt-4"
  - provider_id: 1, model: "gpt-3.5-turbo"
```

**After OpenRouter Import:**
```
providers:
  - id: 1, provider: "openai", platform: "requesty"
  - id: 2, provider: "openai", platform: "openrouter"
models:
  - provider_id: 1, model: "gpt-4"          # Requesty version
  - provider_id: 1, model: "gpt-3.5-turbo"  # Requesty version
  - provider_id: 2, model: "gpt-4"          # OpenRouter version
  - provider_id: 2, model: "gpt-3.5-turbo"  # OpenRouter version
```

### Scenario 2: New Provider Only on OpenRouter

**Import Output:**
```
✅ Added new provider: anthropic (platform: openrouter)
✅ Added model: anthropic/claude-3-sonnet
```

### Scenario 3: Provider Exists on Requesty, Adding to OpenRouter

**Import Output:**
```
ℹ️  Provider 'anthropic' exists for platform(s): requesty. Creating separate OpenRouter entry...
✅ Added new provider: anthropic (platform: openrouter)
ℹ️  Model 'claude-3-sonnet' exists for provider 'anthropic' on platform(s): requesty. Adding OpenRouter version...
✅ Added model: anthropic/claude-3-sonnet (platform: openrouter)
```

## Benefits

### 1. No Data Conflicts
- Same provider/model names can exist on both platforms
- Each platform maintains its own pricing, descriptions, etc.
- No accidental overwrites or conflicts

### 2. Clear Separation
- Easy to filter by platform in API queries
- Clear audit trail of which platform provides what
- Independent management of each platform's data

### 3. Flexible Querying
```python
# Get all OpenRouter providers
openrouter_providers = session.query(Provider).filter_by(platform="openrouter")

# Get all Requesty models
requesty_models = session.query(Model).join(Provider).filter(
    Provider.platform == "requesty"
)

# Get specific provider on specific platform
openai_openrouter = session.query(Provider).filter(
    Provider.provider == "openai",
    Provider.platform == "openrouter"
).first()
```

## Import Script Features

### 1. Pre-Import Analysis
- Shows existing platform data before import
- Identifies potential conflicts
- Provides statistics by platform

### 2. Smart Conflict Detection
- Detects providers that exist on other platforms
- Shows informative messages during import
- Continues processing without errors

### 3. Comprehensive Statistics
```
📊 Import Statistics:
   • Providers added (OpenRouter): 25
   • Providers skipped (already exist): 5
   • Models added (OpenRouter): 150
   • Models skipped (already exist): 10
   • Errors: 0

📊 Final Platform Summary:
   • OpenRouter platform: 30 providers, 160 models
   • Requesty platform: 20 providers, 120 models
   • Providers available on both platforms: 15
```

## Testing

### 1. Test Platform Logic
```bash
python scripts/test_platform_logic.py
```

### 2. Test OpenRouter Import
```bash
python scripts/test_openrouter_script.py  # Safe test, no DB changes
python scripts/add_openrouter_providers_models.py  # Actual import
```

## API Usage

### Filter by Platform
```python
# In provider_service.py
def list_providers(self, platform: Optional[str] = None):
    request = provider_pb2.ListRequest()
    if platform:
        request.platform = platform
    return self.stub.ListProviders(request)
```

### Usage in Routes
```python
# Get only OpenRouter providers
providers = await provider_service.list_providers(platform="openrouter")

# Get only Requesty providers  
providers = await provider_service.list_providers(platform="requesty")

# Get all providers (no platform filter)
providers = await provider_service.list_providers()
```

This platform-aware approach ensures clean separation between Requesty and OpenRouter data while maintaining full functionality for both platforms.
