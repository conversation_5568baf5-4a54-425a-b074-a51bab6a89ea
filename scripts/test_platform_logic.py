#!/usr/bin/env python3
"""
Test script to verify platform-aware logic for providers and models.
This script tests the database queries and logic without making actual changes.
"""

import sys
import os
from typing import Dict, List

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.models.provider import Provider, Model
from app.db.session import Session<PERSON>ocal

def test_platform_queries():
    """Test platform-aware database queries."""
    session = SessionLocal()
    
    try:
        print("🔍 Testing platform-aware database queries...")
        print("=" * 60)
        
        # Test 1: Check providers by platform
        print("\n1. Testing provider queries by platform:")
        
        openrouter_providers = session.query(Provider).filter_by(platform="openrouter").all()
        requesty_providers = session.query(Provider).filter_by(platform="requesty").all()
        
        print(f"   • OpenRouter providers: {len(openrouter_providers)}")
        print(f"   • Requesty providers: {len(requesty_providers)}")
        
        # Test 2: Check for provider name conflicts across platforms
        print("\n2. Testing provider name conflicts across platforms:")
        
        all_providers = session.query(Provider).all()
        provider_names_by_platform = {}
        
        for provider in all_providers:
            platform = provider.platform
            if platform not in provider_names_by_platform:
                provider_names_by_platform[platform] = set()
            provider_names_by_platform[platform].add(provider.provider)
        
        # Find common provider names
        if len(provider_names_by_platform) >= 2:
            platforms = list(provider_names_by_platform.keys())
            common_names = set.intersection(*provider_names_by_platform.values())
            
            if common_names:
                print(f"   • Providers that exist on multiple platforms: {len(common_names)}")
                for name in sorted(common_names)[:5]:  # Show first 5
                    platforms_for_name = []
                    for platform, names in provider_names_by_platform.items():
                        if name in names:
                            platforms_for_name.append(platform)
                    print(f"     - '{name}': {', '.join(platforms_for_name)}")
                if len(common_names) > 5:
                    print(f"     ... and {len(common_names) - 5} more")
            else:
                print("   • No provider name conflicts found")
        else:
            print("   • Not enough platforms to check for conflicts")
        
        # Test 3: Check models by platform
        print("\n3. Testing model queries by platform:")
        
        openrouter_models = session.query(Model).join(Provider).filter(
            Provider.platform == "openrouter"
        ).count()
        
        requesty_models = session.query(Model).join(Provider).filter(
            Provider.platform == "requesty"
        ).count()
        
        print(f"   • OpenRouter models: {openrouter_models}")
        print(f"   • Requesty models: {requesty_models}")
        
        # Test 4: Test specific provider lookup (platform-aware)
        print("\n4. Testing platform-aware provider lookup:")
        
        test_provider_name = "openai"  # Common provider name
        
        openai_openrouter = session.query(Provider).filter(
            Provider.provider == test_provider_name,
            Provider.platform == "openrouter"
        ).first()
        
        openai_requesty = session.query(Provider).filter(
            Provider.provider == test_provider_name,
            Provider.platform == "requesty"
        ).first()
        
        print(f"   • '{test_provider_name}' on OpenRouter: {'Found' if openai_openrouter else 'Not found'}")
        print(f"   • '{test_provider_name}' on Requesty: {'Found' if openai_requesty else 'Not found'}")
        
        if openai_openrouter and openai_requesty:
            print(f"   • Different provider IDs: {openai_openrouter.id} vs {openai_requesty.id}")
            
            # Check models for each
            openrouter_models_count = session.query(Model).filter_by(
                provider_id=openai_openrouter.id
            ).count()
            
            requesty_models_count = session.query(Model).filter_by(
                provider_id=openai_requesty.id
            ).count()
            
            print(f"   • Models on OpenRouter: {openrouter_models_count}")
            print(f"   • Models on Requesty: {requesty_models_count}")
        
        # Test 5: Test model uniqueness logic
        print("\n5. Testing model uniqueness across platforms:")
        
        # Find a common model name
        sample_models = session.query(Model).join(Provider).limit(10).all()
        
        if sample_models:
            test_model = sample_models[0]
            test_model_name = test_model.model
            test_provider_name = test_model.provider.provider
            
            print(f"   • Testing with model '{test_model_name}' from provider '{test_provider_name}'")
            
            # Check if this model exists for the same provider on different platforms
            same_model_different_platforms = session.query(Model).join(Provider).filter(
                Provider.provider == test_provider_name,
                Model.model == test_model_name
            ).all()
            
            if len(same_model_different_platforms) > 1:
                platforms = [m.provider.platform for m in same_model_different_platforms]
                print(f"   • Model exists on platforms: {', '.join(set(platforms))}")
            else:
                print(f"   • Model only exists on one platform: {test_model.provider.platform}")
        
        print("\n✅ All platform-aware queries completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {str(e)}")
        return False
        
    finally:
        session.close()

def simulate_import_logic():
    """Simulate the import logic to test platform-aware uniqueness."""
    session = SessionLocal()
    
    try:
        print("\n🧪 Simulating import logic...")
        print("=" * 60)
        
        # Simulate checking for existing provider
        test_provider_name = "anthropic"
        test_platform = "openrouter"
        
        print(f"\n• Simulating import of provider '{test_provider_name}' for platform '{test_platform}'")
        
        # Check if provider exists for this platform
        existing_provider = session.query(Provider).filter(
            Provider.provider == test_provider_name,
            Provider.platform == test_platform
        ).first()
        
        if existing_provider:
            print(f"  ✓ Provider '{test_provider_name}' already exists for platform '{test_platform}'")
        else:
            print(f"  → Provider '{test_provider_name}' does not exist for platform '{test_platform}'")
            
            # Check if it exists for other platforms
            other_platform_providers = session.query(Provider).filter(
                Provider.provider == test_provider_name,
                Provider.platform != test_platform
            ).all()
            
            if other_platform_providers:
                platforms = [p.platform for p in other_platform_providers]
                print(f"  ℹ️  Provider exists on other platforms: {', '.join(platforms)}")
                print(f"  → Would create new provider entry for platform '{test_platform}'")
            else:
                print(f"  → Would create first provider entry for '{test_provider_name}'")
        
        print("\n✅ Import logic simulation completed!")
        return True
        
    except Exception as e:
        print(f"❌ Error during simulation: {str(e)}")
        return False
        
    finally:
        session.close()

def main():
    """Run all tests."""
    print("🚀 Testing platform-aware logic for providers and models...")
    
    success = True
    
    # Test database queries
    if not test_platform_queries():
        success = False
    
    # Test import logic simulation
    if not simulate_import_logic():
        success = False
    
    print("\n" + "=" * 60)
    if success:
        print("✅ All tests passed! Platform-aware logic is working correctly.")
        return 0
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
